# Flow Balance 货币管理业务流程

## 概述

本文档描述了 Flow Balance 应用中货币管理功能的完整业务流程，包括用户注册、初始设置、货币维护和使用流程。

## 核心设计理念

### 1. 用户可用货币管理
- **原则**：用户只能使用自己设置的可用货币
- **目的**：简化界面，避免混乱，确保数据一致性
- **实现**：通过 `UserCurrency` 表管理用户可用货币列表

### 2. 本位币概念
- **定义**：用户的主要货币，用于汇总计算和报告生成
- **要求**：本位币必须在用户可用货币列表中
- **作用**：所有其他货币的金额会自动转换为本位币显示

### 3. 汇率管理
- **范围**：只能为用户可用货币设置汇率
- **方向**：主要设置其他货币到本位币的汇率
- **用途**：用于多货币金额的统一计算和显示

## 业务流程

### 1. 用户注册流程

```
用户注册 → 创建账户 → 跳转到初始设置页面
```

**详细步骤：**
1. 用户填写注册信息（邮箱、密码）
2. 系统创建用户账户，但不设置本位币
3. 注册成功后跳转到登录页面，带有 `redirect=setup` 参数
4. 用户登录后自动跳转到初始设置页面

### 2. 初始设置流程

```
选择可用货币 → 设置本位币 → 完成设置 → 进入应用
```

**第一步：选择可用货币**
- 显示所有全局货币列表
- 分为"常用货币"和"其他货币"两个区域
- 用户可以选择多种货币
- 至少需要选择一种货币才能继续

**第二步：设置本位币**
- 从已选择的货币中选择一种作为本位币
- 本位币用于汇总计算和报告生成
- 必须选择本位币才能完成设置

**完成设置：**
- 系统保存用户可用货币列表
- 设置用户的本位币
- 跳转到仪表板

### 3. 货币管理流程

**访问路径：** 设置 → 货币管理

**功能包括：**
1. **查看已选择货币**
   - 显示当前可用的货币列表
   - 显示每种货币的基本信息（代码、名称、符号）

2. **添加新货币**
   - 从全局货币列表中选择
   - 只显示未添加的货币
   - 一键添加到可用列表

3. **删除货币**
   - 检查是否为本位币（本位币不能删除）
   - 检查是否有相关交易记录
   - 检查是否有相关汇率设置
   - 满足条件才允许删除

**业务规则：**
- 本位币不能被删除
- 有交易记录的货币不能删除
- 有汇率设置的货币不能删除
- 建议至少保留一种主要货币

### 4. 本位币设置流程

**访问路径：** 设置 → 偏好设置

**流程：**
1. 只显示用户可用货币列表
2. 用户选择新的本位币
3. 系统更新用户设置
4. 影响后续的汇总计算和显示

**限制：**
- 只能从可用货币中选择
- 如果没有可用货币，提示用户先添加货币

### 5. 汇率管理流程

**访问路径：** 设置 → 汇率管理

**功能包括：**
1. **查看现有汇率**
   - 显示已设置的汇率列表
   - 按货币对分组显示

2. **添加汇率**
   - 源货币和目标货币只能从用户可用货币中选择
   - 设置汇率值和生效日期
   - 可添加备注信息

3. **编辑汇率**
   - 修改汇率值
   - 更新生效日期
   - 修改备注

4. **删除汇率**
   - 删除不需要的汇率设置

**自动检测缺失汇率：**
- 系统自动检测需要但未设置的汇率
- 提供快速设置入口
- 优先提示到本位币的汇率

### 6. 交易和账户管理中的货币使用

**货币选择限制：**
- 所有货币选择下拉框只显示用户可用货币
- 包括：交易记录、账户设置、汇率设置等

**显示和计算：**
- 原始金额按原货币显示
- 汇总金额转换为本位币显示
- 如果缺少汇率，显示警告信息

## 数据模型

### 核心表结构

```sql
-- 用户可用货币表
UserCurrency {
  id: String (主键)
  userId: String (用户ID)
  currencyCode: String (货币代码)
  isActive: Boolean (是否启用)
  order: Int (显示顺序)
  createdAt: DateTime
  updatedAt: DateTime
}

-- 用户设置表（修改）
UserSettings {
  baseCurrencyCode: String? (本位币，可为空)
  // 其他字段...
}
```

### 关键约束

1. **唯一性约束**：同一用户的同一货币只能有一条记录
2. **外键约束**：货币代码必须存在于全局货币表中
3. **业务约束**：本位币必须在用户可用货币列表中

## API 接口

### 用户货币管理
- `GET /api/user/currencies` - 获取用户可用货币
- `POST /api/user/currencies` - 添加货币
- `PUT /api/user/currencies` - 批量设置货币
- `DELETE /api/user/currencies/[code]` - 删除货币
- `PATCH /api/user/currencies/[code]` - 更新货币设置

### 全局货币
- `GET /api/currencies` - 获取所有货币（标记用户已选择状态）

### 用户设置
- `PUT /api/user/settings` - 更新用户设置（包括本位币）

## 用户体验优化

### 1. 引导流程
- 新用户注册后必须完成初始设置
- 提供清晰的步骤指示和进度显示
- 常用货币优先显示

### 2. 错误处理
- 友好的错误提示信息
- 详细的操作限制说明
- 提供解决方案建议

### 3. 数据一致性
- 自动检测和提示缺失的汇率
- 防止删除有依赖关系的货币
- 确保本位币的有效性

### 4. 性能优化
- 缓存用户可用货币列表
- 批量操作支持
- 异步数据加载

## 安全考虑

### 1. 权限控制
- 用户只能管理自己的货币设置
- API 接口都需要用户认证
- 防止越权操作

### 2. 数据验证
- 货币代码有效性验证
- 汇率数值合理性检查
- 防止恶意数据输入

### 3. 事务处理
- 批量操作使用数据库事务
- 确保数据一致性
- 失败时自动回滚

## 未来扩展

### 1. 自定义货币
- 支持用户创建自定义货币
- 加密货币支持
- 商品货币支持

### 2. 汇率自动更新
- 集成第三方汇率 API
- 定时自动更新汇率
- 历史汇率查询

### 3. 多本位币支持
- 支持设置多个本位币
- 按场景切换本位币
- 多维度财务报告

## 总结

通过实现完整的货币管理功能，Flow Balance 现在提供了：

1. **清晰的业务流程**：从注册到使用的完整引导
2. **灵活的货币管理**：用户可以自定义可用货币
3. **一致的用户体验**：所有货币选择都基于用户设置
4. **可靠的数据处理**：完善的验证和错误处理机制

这个设计确保了应用的易用性、数据一致性和未来的可扩展性。
